# https://github.com/docker-library/wordpress/blob/master/latest/php8.1/fpm/Dockerfile

FROM php:8.1-fpm

LABEL application=wordpress-nginx

# persistent dependencies
RUN set -eux; \
	apt-get update; \
	apt-get install -y --no-install-recommends \
	# Ghostscript is required for rendering PDF previews
	ghostscript \
	; \
	rm -rf /var/lib/apt/lists/*

# install the PHP extensions we need (https://make.wordpress.org/hosting/handbook/handbook/server-environment/#php-extensions)
RUN set -ex; \
	\
	savedAptMark="$(apt-mark showmanual)"; \
	\
	apt-get update; \
	apt-get install -y --no-install-recommends \
        libavif-dev \
	libfreetype6-dev \
	libicu-dev \
	libjpeg-dev \
	libmagickwand-dev \
	libpng-dev \
	libwebp-dev \
	libzip-dev \
	; \
	\
	docker-php-ext-configure gd \
        --with-avif \
	--with-freetype \
	--with-jpeg \
	--with-webp \
	; \
	docker-php-ext-install -j "$(nproc)" \
	bcmath \
	exif \
	gd \
	intl \
	mysqli \
	zip \
	; \
	# https://pecl.php.net/package/imagick
	# https://github.com/Imagick/imagick/commit/5ae2ecf20a1157073bad0170106ad0cf74e01cb6 (causes a lot of build failures, but strangely only intermittent ones 🤔)
	# see also https://github.com/Imagick/imagick/pull/641
	# this is "pecl install imagick-3.7.0", but by hand so we can apply a small hack / part of the above commit
	curl -fL -o imagick.tgz 'https://pecl.php.net/get/imagick-3.7.0.tgz'; \
	echo '5a364354109029d224bcbb2e82e15b248be9b641227f45e63425c06531792d3e *imagick.tgz' | sha256sum -c -; \
	tar --extract --directory /tmp --file imagick.tgz imagick-3.7.0; \
	grep '^//#endif$' /tmp/imagick-3.7.0/Imagick.stub.php; \
	test "$(grep -c '^//#endif$' /tmp/imagick-3.7.0/Imagick.stub.php)" = '1'; \
	sed -i -e 's!^//#endif$!#endif!' /tmp/imagick-3.7.0/Imagick.stub.php; \
	grep '^//#endif$' /tmp/imagick-3.7.0/Imagick.stub.php && exit 1 || :; \
	docker-php-ext-install /tmp/imagick-3.7.0; \
	docker-php-ext-enable imagick; \
	rm -rf imagick.tgz /tmp/imagick-3.7.0; \
	\
	# some misbehaving extensions end up outputting to stdout 🙈 (https://github.com/docker-library/wordpress/issues/669#issuecomment-993945967)
	out="$(php -r 'exit(0);')"; \
	[ -z "$out" ]; \
	err="$(php -r 'exit(0);' 3>&1 1>&2 2>&3)"; \
	[ -z "$err" ]; \
	\
	extDir="$(php -r 'echo ini_get("extension_dir");')"; \
	[ -d "$extDir" ]; \
	# reset apt-mark's "manual" list so that "purge --auto-remove" will remove all build dependencies
	apt-mark auto '.*' > /dev/null; \
	apt-mark manual $savedAptMark; \
	ldd "$extDir"/*.so \
	| awk '/=>/ { so = $(NF-1); if (index(so, "/usr/local/") == 1) { next }; gsub("^/(usr/)?", "", so); print so }' \
	| sort -u \
	| xargs -r dpkg-query --search \
	| cut -d: -f1 \
	| sort -u \
	| xargs -rt apt-mark manual; \
	\
	apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false; \
	rm -rf /var/lib/apt/lists/*; \
	\
	! { ldd "$extDir"/*.so | grep 'not found'; }; \
	# check for output like "PHP Warning:  PHP Startup: Unable to load dynamic library 'foo' (tried: ...)
	err="$(php --version 3>&1 1>&2 2>&3)"; \
	[ -z "$err" ]

# Install essential debugging and system monitoring tools for PHP 8.1
# This ensures the validation script works properly and provides troubleshooting capabilities
RUN set -eux; \
	apt-get update; \
	apt-get install -y --no-install-recommends \
	# Core process and network monitoring tools (required by validation script)
	procps \
	net-tools \
	iproute2 \
	# Network debugging and connectivity testing
	curl \
	wget \
	telnet \
	netcat-openbsd \
	dnsutils \
	# File and system inspection tools
	less \
	vim-tiny \
	htop \
	tree \
	# Archive and file manipulation
	unzip \
	# Additional networking tools
	iputils-ping \
	traceroute \
	# Text processing tools useful for debugging
	jq \
	# System info tools
	lsof \
	; \
	# Clean up to keep image size reasonable
	rm -rf /var/lib/apt/lists/*; \
	\
	# Verify essential tools are available for the validation script
	echo "=== Verifying Debug Tools Installation for PHP 8.1 ==="; \
	command -v ps && echo "✓ ps available (required by validation script)"; \
	command -v netstat && echo "✓ netstat available (required by validation script)"; \
	command -v ss && echo "✓ ss available (required by validation script)"; \
	command -v pgrep && echo "✓ pgrep available (required by validation script)"; \
	command -v curl && echo "✓ curl available"; \
	command -v wget && echo "✓ wget available"; \
	command -v nc && echo "✓ netcat available"; \
	command -v ping && echo "✓ ping available"; \
	command -v top && echo "✓ top available"; \
	command -v htop && echo "✓ htop available"; \
	command -v jq && echo "✓ jq available"; \
	command -v lsof && echo "✓ lsof available"; \
	echo "=== Debug Tools Installation Complete for PHP 8.1 ==="

# set recommended PHP.ini settings
# see https://secure.php.net/manual/en/opcache.installation.php
RUN set -eux; \
	docker-php-ext-enable opcache; \
	{ \
	echo 'opcache.memory_consumption=128'; \
	echo 'opcache.interned_strings_buffer=8'; \
	echo 'opcache.max_accelerated_files=4000'; \
	echo 'opcache.revalidate_freq=2'; \
	echo 'opcache.fast_shutdown=1'; \
	} > /usr/local/etc/php/conf.d/opcache-recommended.ini
# https://wordpress.org/support/article/editing-wp-config-php/#configure-error-logging
RUN { \
	# https://www.php.net/manual/en/errorfunc.constants.php
	# https://github.com/docker-library/wordpress/issues/420#issuecomment-517839670
	echo 'error_reporting = E_ERROR | E_WARNING | E_PARSE | E_CORE_ERROR | E_CORE_WARNING | E_COMPILE_ERROR | E_COMPILE_WARNING | E_RECOVERABLE_ERROR'; \
	echo 'display_errors = Off'; \
	echo 'display_startup_errors = Off'; \
	echo 'log_errors = On'; \
	echo 'error_log = /dev/stderr'; \
	echo 'log_errors_max_len = 1024'; \
	echo 'ignore_repeated_errors = On'; \
	echo 'ignore_repeated_source = Off'; \
	echo 'html_errors = Off'; \
	} > /usr/local/etc/php/conf.d/error-logging.ini

# FIX: Resolve PHP-FPM configuration conflict between www.conf and zz-docker.conf
# This prevents intermittent "Connection refused" errors and nginx worker_connections exhaustion
# Issue: Both files define the same [www] pool with different listen directives, causing binding conflicts
RUN set -eux; \
	echo "=== Applying PHP-FPM Configuration Fix for PHP 8.1 ==="; \
	\
	# Display current configuration state for debugging
	echo "Current PHP-FPM configuration files:"; \
	ls -la /usr/local/etc/php-fpm.d/ || echo "No php-fpm.d directory found yet"; \
	\
	# Check if www.conf exists and contains conflicting directive
	if [ -f "/usr/local/etc/php-fpm.d/www.conf" ]; then \
		echo "Found www.conf, analyzing configuration..."; \
		\
		# Show current listen directives for transparency
		echo "Current listen directives in www.conf:"; \
		grep -n "listen" /usr/local/etc/php-fpm.d/www.conf || echo "  No listen directives found"; \
		\
		# Fix the specific conflict: comment out 127.0.0.1:9000 listen directive
		if grep -q '^listen = 127\.0\.0\.1:9000' /usr/local/etc/php-fpm.d/www.conf; then \
			echo "FIXING: Commenting out conflicting listen directive in www.conf"; \
			sed -i 's/^listen = 127\.0\.0\.1:9000/;listen = 127.0.0.1:9000 ; Commented to prevent conflict with Docker zz-docker.conf/' /usr/local/etc/php-fpm.d/www.conf; \
		elif grep -q '^;listen = 127\.0\.0\.1:9000' /usr/local/etc/php-fpm.d/www.conf; then \
			echo "ALREADY FIXED: Conflicting listen directive already commented in www.conf"; \
		else \
			echo "INFO: No conflicting 127.0.0.1:9000 listen directive found in www.conf"; \
		fi; \
		\
		# Verification: ensure no active conflicting listen directive remains
		if grep -q '^listen = 127\.0\.0\.1:9000' /usr/local/etc/php-fpm.d/www.conf; then \
			echo "ERROR: Failed to resolve listen directive conflict in www.conf"; \
			echo "Current www.conf listen directives:"; \
			grep -n "listen" /usr/local/etc/php-fpm.d/www.conf; \
			exit 1; \
		fi; \
	else \
		echo "INFO: www.conf not found - no conflicts to resolve"; \
	fi; \
	\
	# Test PHP-FPM configuration syntax
	echo "Validating PHP-FPM configuration syntax..."; \
	php-fpm -t; \
	\
	# Display final configuration summary
	echo "=== Configuration Summary ==="; \
	if [ -f "/usr/local/etc/php-fpm.d/www.conf" ]; then \
		echo "www.conf final listen configuration:"; \
		grep -n "listen.*127.0.0.1:9000" /usr/local/etc/php-fpm.d/www.conf || echo "  ✓ No active conflicting listen directive"; \
	fi; \
	if [ -f "/usr/local/etc/php-fpm.d/zz-docker.conf" ]; then \
		echo "zz-docker.conf configuration:"; \
		cat /usr/local/etc/php-fpm.d/zz-docker.conf; \
	else \
		echo "  Note: zz-docker.conf will be created by Docker at runtime"; \
	fi; \
	\
	# Final syntax validation
	echo "Final PHP-FPM configuration test:"; \
	php-fpm -tt 2>&1 | head -5 || true; \
	echo "=== PHP 8.1 FPM Configuration Fix Complete ===";

RUN set -eux; \
	version='6.4.3'; \
	sha1='ee3bc3a73ab3cfa535c46f111eb641b3467fa44e'; \
	\
	curl -o wordpress.tar.gz -fL "https://wordpress.org/wordpress-$version.tar.gz"; \
	echo "$sha1 *wordpress.tar.gz" | sha1sum -c -; \
	\
	# upstream tarballs include ./wordpress/ so this gives us /usr/src/wordpress
	tar -xzf wordpress.tar.gz -C /usr/src/; \
	rm wordpress.tar.gz; \
	\
	# https://wordpress.org/support/article/htaccess/
	[ ! -e /usr/src/wordpress/.htaccess ]; \
	{ \
	echo '# BEGIN WordPress'; \
	echo ''; \
	echo 'RewriteEngine On'; \
	echo 'RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]'; \
	echo 'RewriteBase /'; \
	echo 'RewriteRule ^index\.php$ - [L]'; \
	echo 'RewriteCond %{REQUEST_FILENAME} !-f'; \
	echo 'RewriteCond %{REQUEST_FILENAME} !-d'; \
	echo 'RewriteRule . /index.php [L]'; \
	echo ''; \
	echo '# END WordPress'; \
	} > /usr/src/wordpress/.htaccess; \
	\
	chown -R www-data:www-data /usr/src/wordpress; \
	# pre-create wp-content (and single-level children) for folks who want to bind-mount themes, etc so permissions are pre-created properly instead of root:root
	# wp-content/cache: https://github.com/docker-library/wordpress/issues/534#issuecomment-705733507
	mkdir wp-content; \
	for dir in /usr/src/wordpress/wp-content/*/ cache; do \
	dir="$(basename "${dir%/}")"; \
	mkdir "wp-content/$dir"; \
	done; \
	chown -R www-data:www-data wp-content; \
	chmod -R 1777 wp-content

# Add a comprehensive debug script for PHP 8.1 container troubleshooting
RUN set -eux; \
	{ \
	echo '#!/bin/bash'; \
	echo '# WordPress PHP 8.1 FPM Container Debug Helper'; \
	echo 'echo "=== PHP 8.1 FPM Container Debug Information ==="'; \
	echo 'echo "Container Image: php:8.1-fpm with WordPress"'; \
	echo 'echo "Build Date: $(date)"'; \
	echo 'echo ""'; \
	echo 'echo "PHP Version & Extensions:"'; \
	echo 'php --version | head -1'; \
	echo 'echo "Extensions: $(php -m | grep -E '\''(mysqli|gd|imagick|zip|opcache)'\'' | tr '\''\n'\'' '\'' '\'' | sed '\''s/ $//'\'')'; \
	echo 'echo ""'; \
	echo 'echo "PHP-FPM Status:"'; \
	echo 'php-fpm -t 2>/dev/null && echo "✓ Configuration OK" || echo "✗ Configuration Error"'; \
	echo 'echo ""'; \
	echo 'echo "PHP-FPM Configuration Files:"'; \
	echo 'echo "Files: $(ls -1 /usr/local/etc/php-fpm.d/*.conf 2>/dev/null | wc -l) config files found"'; \
	echo 'echo "Listen directives:"'; \
	echo 'grep -n '\''listen'\'' /usr/local/etc/php-fpm.d/*.conf 2>/dev/null || echo "  No listen directives found"'; \
	echo 'echo ""'; \
	echo 'echo "Active Processes:"'; \
	echo 'ps aux | grep -E '\''(php-fpm|nginx)'\'' | grep -v grep'; \
	echo 'echo ""'; \
	echo 'echo "Network Status:"'; \
	echo 'echo "Listening Ports:"'; \
	echo 'netstat -tuln 2>/dev/null | grep LISTEN || ss -tuln 2>/dev/null | grep LISTEN || echo "  No port info available"'; \
	echo 'echo "Port 9000 Status:"'; \
	echo 'netstat -tuln 2>/dev/null | grep :9000 && echo "✓ Port 9000 listening" || echo "⚠ Port 9000 not found in netstat"'; \
	echo 'timeout 2 bash -c '\''</dev/tcp/127.0.0.1/9000'\'' 2>/dev/null && echo "✓ Port 9000 connectable" || echo "⚠ Port 9000 not connectable"'; \
	echo 'echo ""'; \
	echo 'echo "System Resources:"'; \
	echo 'echo "Memory: $(free -h | grep Mem | awk '\''{print $3 "/" $2}'\'')"'; \
	echo 'echo "Load: $(uptime | sed '\''s/.*load average: //'\'')"'; \
	echo 'echo "Disk: $(df -h / | tail -1 | awk '\''{print $3 "/" $2 " (" $5 " used)"}'\'')"'; \
	echo 'echo ""'; \
	echo 'echo "WordPress Status:"'; \
	echo 'if [ -f /var/www/html/wp-config.php ]; then'; \
	echo '  echo "✓ WordPress installed"'; \
	echo '  echo "Files: $(find /var/www/html -name '\''*.php'\'' | wc -l) PHP files"'; \
	echo 'else'; \
	echo '  echo "⚠ WordPress not found in /var/www/html"'; \
	echo 'fi'; \
	echo 'echo ""'; \
	echo 'echo "Debug Tools Available:"'; \
	echo 'echo "$(command -v ps >/dev/null && echo "✓ ps" || echo "✗ ps") $(command -v netstat >/dev/null && echo "✓ netstat" || echo "✗ netstat") $(command -v ss >/dev/null && echo "✓ ss" || echo "✗ ss") $(command -v curl >/dev/null && echo "✓ curl" || echo "✗ curl") $(command -v htop >/dev/null && echo "✓ htop" || echo "✗ htop")"'; \
	echo 'echo "=== PHP 8.1 Debug Complete ==="'; \
	} > /usr/local/bin/debug-container; \
	chmod +x /usr/local/bin/debug-container; \
	echo "✓ PHP 8.1 debug script created at /usr/local/bin/debug-container"

VOLUME /var/www/html

# Add helpful labels for identification and debugging
LABEL maintainer="WordPress PHP 8.1 FPM with Debug Tools" \
      php.version="8.1" \
      wordpress.version="6.4.3" \
      version="1.0" \
      description="WordPress PHP 8.1 FPM container with PHP-FPM configuration fix and comprehensive debug tools" \
      debug.tools="ps,netstat,ss,curl,wget,nc,ping,htop,git,jq,lsof" \
      config.fix="php-fpm-listen-conflict-resolved" \
      validation.script="compatible"