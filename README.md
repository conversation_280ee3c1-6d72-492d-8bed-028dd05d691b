# Docker AWS Deployment Guide

This document provides comprehensive instructions for building and deploying Docker images to AWS ECR using our Makefile-based workflow.

## Prerequisites

Before you begin, ensure you have the following installed:

- Docker (20.10.x or later)
- AWS CLI (2.x or later)
- Git
- Make

Also ensure you have:
- AWS credentials configured (`~/.aws/credentials` or environment variables)
- Sufficient permissions to push/pull from ECR repositories
- Docker daemon running

## Configuration

### Environment Variables

You can customize the build process using these environment variables:

```bash
# AWS Settings
REGION=ap-southeast-2          # AWS region for ECR
ACCOUNT_ID=************       # AWS account ID (default: PROD)

# Application Settings
APP_NAME=wordpress-nginx      # Application name
PHP_VERSION=php83            # PHP version (supported: php81, php83)
APP_VERSION=<git-hash>       # Defaults to git commit hash
```

### AWS Account Setup

The Makefile supports two environments:
- Test (Account ID: ************)
- Production (Account ID: ************)

## Basic Usage

### 1. Building Images

Build for test environment:
```bash
# Using default PHP version (php81)
make build/test

# Specifying PHP version
make build/test PHP_VERSION=php83
```

Build for production:
```bash
make build/prod PHP_VERSION=php83
```

### 2. Pushing Images to ECR

Push to test environment:
```bash
make push/test
```

Push to production:
```bash
make push/prod
```

Note: `push` command includes automatic login to ECR.

### 3. ECR Authentication

Login to ECR:
```bash
make login/test    # For test environment
make login/prod    # For production environment
```

Logout from ECR:
```bash
make logout/test
make logout/prod
```

## Advanced Usage

### Version Information

Display current version information:
```bash
make version
```

Output example:
```json
{
  "Version": "a1b2c3d",
  "Branch": "main",
  "PHP": "php83",
  "Timestamp": "********-143022"
}
```

### Testing and Linting

Run tests:
```bash
make test
```

Run code linting:
```bash
make lint
```

### Cleanup

Remove dangling images:
```bash
make clean
```

## Common Workflows

### 1. Complete Build and Deploy to Test

```bash
# Build and push to test environment
make build/test PHP_VERSION=php83
make push/test
```

### 2. Production Deployment

```bash
# Build and push to production
make build/prod PHP_VERSION=php83
make push/prod
```

### 3. Development Workflow

```bash
# Build local development image
make build/test

# Run container locally
make run

# Run tests
make test
```

## Troubleshooting

### Common Issues

1. ECR Login Failed
```bash
# Solution: Check AWS credentials
aws configure list
# Or try re-logging in
make logout/test && make login/test
```

2. Build Failures
```bash
# Check Docker daemon
docker info

# Clean up old images
make clean
```

3. Push Failures
```bash
# Verify ECR repository exists
aws ecr describe-repositories

# Check image exists locally
docker images
```

### Getting Help

Display help message:
```bash
make help
```

## Best Practices

1. **Version Control**
   - Always specify PHP version explicitly
   - Use meaningful commit messages (they become part of the version)

2. **Testing**
   - Run tests before pushing to any environment
   - Use test environment before production deployment

3. **Security**
   - Regularly rotate AWS credentials
   - Never commit sensitive information
   - Always review Docker images before production push

4. **Maintenance**
   - Regular cleanup using `make clean`
   - Keep PHP versions up to date
   - Monitor ECR repository size

## Directory Structure

```
.
├── Makefile                # Main build configuration
├── Dockerfile             # Docker image definition
├── docker-compose.yml     # Local development setup
└── .dockerignore         # Build exclusions
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## Support

For additional support:
1. Check `make help` for available commands
2. Review AWS ECR documentation