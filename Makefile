# Enhanced Makefile for Docker Compose project with AWS multi-environment support

# Configuration Variables
# ---------------------
# AWS Account IDs
TEST_AWS_ID := ************
PROD_AWS_ID := ************

# Default AWS settings
REGION ?= ap-southeast-2
ACCOUNT_ID ?= $(PROD_AWS_ID)
APP_NAME ?= wordpress-nginx

# Supported PHP versions with their Dockerfile mappings
SUPPORTED_PHP_VERSIONS := php81 php83 php84
PHP_VERSION ?= php83

# Dockerfile mapping for each PHP version
# php81: Uses Dockerfile_php81 (PHP 8.1 with PHP-FPM fixes)
# php83: Uses Dockerfile (PHP 8.3 with PHP-FPM fixes - default) 
# php84: Uses Dockerfile_php84 (PHP 8.4 with enhanced PHP-FPM fixes)
define get_dockerfile
$(if $(filter php81,$(1)),Dockerfile_php81,$(if $(filter php84,$(1)),Dockerfile_php84,Dockerfile))
endef

# Git and versioning
GIT_HASH := $(shell git rev-parse --short HEAD)
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
APP_VERSION ?= $(GIT_HASH)
TIMESTAMP := $(shell date +%Y%m%d-%H%M%S)

# Docker repository
REPO ?= $(ACCOUNT_ID).dkr.ecr.$(REGION).amazonaws.com/$(APP_NAME)

# Color outputs
CYAN := \033[36m
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

# Validation Functions
# ------------------
define validate_environment
    @if ! echo "test prod" | grep -w "$(1)" > /dev/null; then \
        echo "$(RED)Error: Invalid environment '$(1)'. Use 'test' or 'prod'$(RESET)" >&2; \
        exit 1; \
    fi
endef

define validate_php_version
    @if ! echo "$(SUPPORTED_PHP_VERSIONS)" | grep -w "$(1)" > /dev/null; then \
        echo "$(RED)Error: Invalid PHP version '$(1)'. Supported versions: $(SUPPORTED_PHP_VERSIONS)$(RESET)" >&2; \
        exit 1; \
    fi
endef

define show_dockerfile_info
    $(eval DOCKERFILE := $(call get_dockerfile,$(1)))
    @echo "$(BLUE)Using Dockerfile: $(DOCKERFILE) for PHP version: $(1)$(RESET)"
    @if [ "$(1)" = "php84" ]; then \
        echo "$(YELLOW)Note: PHP 8.4 includes enhanced PHP-FPM configuration fixes$(RESET)"; \
    elif [ "$(1)" = "php81" ]; then \
        echo "$(YELLOW)Note: PHP 8.1 includes PHP-FPM configuration fixes for legacy support$(RESET)"; \
    elif [ "$(1)" = "php83" ]; then \
        echo "$(YELLOW)Note: PHP 8.3 includes PHP-FPM configuration fixes (current default)$(RESET)"; \
    fi
endef

# Default target
.DEFAULT_GOAL := help

# Targets
# -------
.PHONY: help version check-deps login/% logout/% build/% push/% run clean test lint build-all/% push-all/%

help: ## Display this help message
	@echo "$(CYAN)Docker AWS Deployment Makefile$(RESET)"
	@echo "$(YELLOW)Usage:$(RESET) make [target] or make [target]/[environment]"
	@echo ""
	@echo "$(YELLOW)Available targets:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(CYAN)%-30s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)Targets with environments (use test or prod):$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+\/%:.*?## / {sub(/%/, "", $$1); printf "  $(CYAN)%-30s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)Environment variables:$(RESET)"
	@echo "  PHP_VERSION: Current: $(PHP_VERSION) (Supported: $(SUPPORTED_PHP_VERSIONS))"
	@echo "  REGION: $(REGION)"
	@echo "  APP_VERSION: $(APP_VERSION)"
	@echo ""
	@echo "$(YELLOW)Dockerfile Mapping:$(RESET)"
	@echo "  php81:        Uses $(CYAN)Dockerfile_php81$(RESET) (with PHP-FPM fixes)"
	@echo "  php83:        Uses $(CYAN)Dockerfile$(RESET) (with PHP-FPM fixes - default)"
	@echo "  php84:        Uses $(CYAN)Dockerfile_php84$(RESET) (with enhanced PHP-FPM fixes)"
	@echo ""
	@echo "$(YELLOW)Examples:$(RESET)"
	@echo "  make build/test PHP_VERSION=php81   # Build PHP 8.1 for test environment"
	@echo "  make build/test PHP_VERSION=php83   # Build PHP 8.3 for test environment"
	@echo "  make build/prod PHP_VERSION=php84   # Build PHP 8.4 for prod environment"
	@echo "  make build-all/test                  # Build all PHP versions for test"
	@echo "  make push-all/prod                   # Push all PHP versions to prod"

version: ## Display version information
	@echo '{'
	@echo '  "Version": "$(APP_VERSION)",'
	@echo '  "Branch": "$(GIT_BRANCH)",'
	@echo '  "PHP": "$(PHP_VERSION)",'
	@echo '  "Dockerfile": "$(call get_dockerfile,$(PHP_VERSION))",'
	@echo '  "Timestamp": "$(TIMESTAMP)"'
	@echo '}'

check-deps: ## Check required dependencies
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Error: docker is required but not installed$(RESET)" >&2; exit 1; }
	@command -v aws >/dev/null 2>&1 || { echo "$(RED)Error: aws-cli is required but not installed$(RESET)" >&2; exit 1; }
	@command -v git >/dev/null 2>&1 || { echo "$(RED)Error: git is required but not installed$(RESET)" >&2; exit 1; }
	@if [ "$(PHP_VERSION)" = "php81" ] && [ ! -f "Dockerfile_php81" ]; then \
		echo "$(RED)Error: Dockerfile_php81 not found for PHP 8.1 build$(RESET)" >&2; \
		exit 1; \
	fi
	@if [ "$(PHP_VERSION)" = "php84" ] && [ ! -f "Dockerfile_php84" ]; then \
		echo "$(RED)Error: Dockerfile_php84 not found for PHP 8.4 build$(RESET)" >&2; \
		exit 1; \
	fi

login/%: check-deps ## Login to AWS ECR for specified environment (test, prod)
	$(call validate_environment,$*)
	$(eval AWS_ID := $(if $(filter test,$*),$(TEST_AWS_ID),$(PROD_AWS_ID)))
	@echo "$(GREEN)Logging in to AWS ECR for $* environment$(RESET)"
	@aws ecr get-login-password --region $(REGION) | docker login --username AWS --password-stdin $(AWS_ID).dkr.ecr.$(REGION).amazonaws.com

logout/%: ## Logout from AWS ECR for specified environment (test, prod)
	$(call validate_environment,$*)
	$(eval AWS_ID := $(if $(filter test,$*),$(TEST_AWS_ID),$(PROD_AWS_ID)))
	@echo "$(GREEN)Logging out from AWS ECR for $* environment$(RESET)"
	@docker logout https://$(AWS_ID).dkr.ecr.$(REGION).amazonaws.com

build/%: check-deps ## Build Docker image for specified environment (test, prod)
	$(call validate_environment,$*)
	$(call validate_php_version,$(PHP_VERSION))
	$(eval AWS_ID := $(if $(filter test,$*),$(TEST_AWS_ID),$(PROD_AWS_ID)))
	$(eval DOCKERFILE := $(call get_dockerfile,$(PHP_VERSION)))
	$(call show_dockerfile_info,$(PHP_VERSION))
	@echo "$(GREEN)Building Docker image for $* environment with $(PHP_VERSION)$(RESET)"
	docker build \
		--build-arg PHP_VERSION=$(PHP_VERSION) \
		--build-arg BUILD_TIMESTAMP=$(TIMESTAMP) \
		--build-arg GIT_COMMIT=$(GIT_HASH) \
		-f $(DOCKERFILE) \
		-t $(AWS_ID).dkr.ecr.$(REGION).amazonaws.com/$(APP_NAME):$(APP_VERSION)-$(PHP_VERSION) \
		-t $(AWS_ID).dkr.ecr.$(REGION).amazonaws.com/$(APP_NAME):$(PHP_VERSION) \
		$(if $(filter php83,$(PHP_VERSION)),-t $(AWS_ID).dkr.ecr.$(REGION).amazonaws.com/$(APP_NAME):latest) \
		.

build-all/%: check-deps ## Build all PHP versions for specified environment (test, prod)
	$(call validate_environment,$*)
	@echo "$(GREEN)Building all PHP versions for $* environment$(RESET)"
	@for php_ver in $(SUPPORTED_PHP_VERSIONS); do \
		echo "$(CYAN)Building $$php_ver...$(RESET)"; \
		$(MAKE) build/$* PHP_VERSION=$$php_ver || exit 1; \
	done
	@echo "$(GREEN)All PHP versions built successfully for $* environment$(RESET)"

push/%: login/% ## Push Docker image to ECR for specified environment (test, prod)
	$(call validate_environment,$*)
	$(call validate_php_version,$(PHP_VERSION))
	$(eval AWS_ID := $(if $(filter test,$*),$(TEST_AWS_ID),$(PROD_AWS_ID)))
	@echo "$(GREEN)Pushing $(PHP_VERSION) Docker images to ECR for $* environment$(RESET)"
	@docker push $(AWS_ID).dkr.ecr.$(REGION).amazonaws.com/$(APP_NAME):$(APP_VERSION)-$(PHP_VERSION)
	@docker push $(AWS_ID).dkr.ecr.$(REGION).amazonaws.com/$(APP_NAME):$(PHP_VERSION)
	@if [ "$(PHP_VERSION)" = "php83" ]; then \
		echo "$(YELLOW)Pushing latest tag (PHP 8.3 is default)$(RESET)"; \
		docker push $(AWS_ID).dkr.ecr.$(REGION).amazonaws.com/$(APP_NAME):latest; \
	fi

push-all/%: login/% ## Push all PHP versions to ECR for specified environment (test, prod)
	$(call validate_environment,$*)
	@echo "$(GREEN)Pushing all PHP versions to ECR for $* environment$(RESET)"
	@for php_ver in $(SUPPORTED_PHP_VERSIONS); do \
		echo "$(CYAN)Pushing $$php_ver...$(RESET)"; \
		$(MAKE) push/$* PHP_VERSION=$$php_ver || exit 1; \
	done
	@echo "$(GREEN)All PHP versions pushed successfully to $* environment$(RESET)"

run: ## Run the Docker container locally
	$(call validate_php_version,$(PHP_VERSION))
	@echo "$(GREEN)Running $(PHP_VERSION) container locally$(RESET)"
	docker run -it --rm $(REPO):$(PHP_VERSION) bash

test: ## Run tests in Docker container
	$(call validate_php_version,$(PHP_VERSION))
	@echo "$(GREEN)Running tests with $(PHP_VERSION)$(RESET)"
	docker run --rm $(REPO):$(PHP_VERSION) vendor/bin/phpunit

lint: ## Run code linting
	$(call validate_php_version,$(PHP_VERSION))
	@echo "$(GREEN)Running code linting with $(PHP_VERSION)$(RESET)"
	docker run --rm $(REPO):$(PHP_VERSION) vendor/bin/phpcs

clean: ## Remove dangling Docker images and clean up
	@echo "$(GREEN)Cleaning up Docker images$(RESET)"
	-docker images -q -f dangling=true -f label=application=$(APP_NAME) | xargs -I ARGS docker rmi -f --no-prune ARGS
	-docker system prune -f --filter "label=application=$(APP_NAME)"

# Convenience targets for specific PHP versions
build-php81/%: ## Build PHP 8.1 (Dockerfile_php81 with fixes)
	$(MAKE) build/$* PHP_VERSION=php81

build-php83/%: ## Build PHP 8.3 (Dockerfile with fixes - default)
	$(MAKE) build/$* PHP_VERSION=php83

build-php84/%: ## Build PHP 8.4 (Dockerfile_php84 with enhanced fixes)
	$(MAKE) build/$* PHP_VERSION=php84

push-php81/%: ## Push PHP 8.1 to ECR
	$(MAKE) push/$* PHP_VERSION=php81

push-php83/%: ## Push PHP 8.3 to ECR
	$(MAKE) push/$* PHP_VERSION=php83

push-php84/%: ## Push PHP 8.4 to ECR
	$(MAKE) push/$* PHP_VERSION=php84

# Status and info targets
show-images: ## Show all built images
	@echo "$(CYAN)WordPress-Nginx Docker Images:$(RESET)"
	@docker images | grep "$(APP_NAME)" | head -20

show-tags/%: ## Show available tags in ECR for environment
	$(call validate_environment,$*)
	$(eval AWS_ID := $(if $(filter test,$*),$(TEST_AWS_ID),$(PROD_AWS_ID)))
	@echo "$(CYAN)Available tags in $* ECR:$(RESET)"
	@aws ecr describe-images --repository-name $(APP_NAME) --region $(REGION) \
		--query 'imageDetails[*].imageTags' --output table 2>/dev/null || \
		echo "$(YELLOW)No images found or repository doesn't exist$(RESET)"

# Testing and validation targets
test-build-all: ## Test build all PHP versions locally (no push)
	@echo "$(CYAN)Testing build for all PHP versions locally$(RESET)"
	@for php_ver in $(SUPPORTED_PHP_VERSIONS); do \
		echo "$(YELLOW)Testing build for $$php_ver...$(RESET)"; \
		$(eval DOCKERFILE := $(call get_dockerfile,$$php_ver)) \
		echo "  Using Dockerfile: $$DOCKERFILE"; \
		docker build -f $$DOCKERFILE -t test-$(APP_NAME):$$php_ver . || exit 1; \
		echo "  ✓ $$php_ver build successful"; \
	done
	@echo "$(GREEN)All PHP versions built successfully for testing$(RESET)"

validate-dockerfiles: ## Validate all required Dockerfiles exist
	@echo "$(CYAN)Validating Dockerfile existence$(RESET)"
	@for php_ver in $(SUPPORTED_PHP_VERSIONS); do \
		$(eval DOCKERFILE := $(call get_dockerfile,$$php_ver)) \
		if [ -f "$$DOCKERFILE" ]; then \
			echo "  ✓ $$DOCKERFILE exists for $$php_ver"; \
		else \
			echo "  ✗ $$DOCKERFILE missing for $$php_ver"; \
			exit 1; \
		fi; \
	done
	@echo "$(GREEN)All required Dockerfiles are present$(RESET)"