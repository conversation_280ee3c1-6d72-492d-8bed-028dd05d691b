# https://github.com/docker-library/wordpress/blob/master/latest/php8.4/fpm/Dockerfile

FROM php:8.4-fpm
LABEL application=wordpress-nginx

# persistent dependencies
RUN set -eux; \
	apt-get update; \
	apt-get install -y --no-install-recommends \
        # Ghostscript is required for rendering PDF previews
	ghostscript \
	; \
	rm -rf /var/lib/apt/lists/*

# install the PHP extensions we need (https://make.wordpress.org/hosting/handbook/handbook/server-environment/#php-extensions)
RUN set -ex; \
	\
	savedAptMark="$(apt-mark showmanual)"; \
	\
	apt-get update; \
	apt-get install -y --no-install-recommends \
		libavif-dev \
		libfreetype6-dev \
		libicu-dev \
		libjpeg-dev \
		libmagickwand-dev \
		libpng-dev \
		libwebp-dev \
		libzip-dev \
	; \
	\
	docker-php-ext-configure gd \
		--with-avif \
		--with-freetype \
		--with-jpeg \
		--with-webp \
	; \
	docker-php-ext-install -j "$(nproc)" \
		bcmath \
		exif \
		gd \
		intl \
		mysqli \
		zip \
	; \
# https://pecl.php.net/package/imagick
	pecl install imagick-3.8.0; \
	docker-php-ext-enable imagick; \
	rm -r /tmp/pear; \
	\
# some misbehaving extensions end up outputting to stdout 🙈 (https://github.com/docker-library/wordpress/issues/669#issuecomment-993945967)
	out="$(php -r 'exit(0);')"; \
	[ -z "$out" ]; \
	err="$(php -r 'exit(0);' 3>&1 1>&2 2>&3)"; \
	[ -z "$err" ]; \
	\
	extDir="$(php -r 'echo ini_get("extension_dir");')"; \
	[ -d "$extDir" ]; \
# reset apt-mark's "manual" list so that "purge --auto-remove" will remove all build dependencies
	apt-mark auto '.*' > /dev/null; \
	apt-mark manual $savedAptMark; \
	ldd "$extDir"/*.so \
		| awk '/=>/ { so = $(NF-1); if (index(so, "/usr/local/") == 1) { next }; gsub("^/(usr/)?", "", so); printf "*%s\n", so }' \
		| sort -u \
		| xargs -r dpkg-query --search \
		| cut -d: -f1 \
		| sort -u \
		| xargs -rt apt-mark manual; \
	\
	apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false; \
	rm -rf /var/lib/apt/lists/*; \
	\
	! { ldd "$extDir"/*.so | grep 'not found'; }; \
# check for output like "PHP Warning:  PHP Startup: Unable to load dynamic library 'foo' (tried: ...)
	err="$(php --version 3>&1 1>&2 2>&3)"; \
	[ -z "$err" ]

# Install comprehensive debugging and system monitoring tools for PHP 8.4
# Essential for container troubleshooting and validation script compatibility
RUN set -eux; \
	apt-get update; \
	apt-get install -y --no-install-recommends \
	# Core process and network monitoring tools (required by validation script)
	procps \
	net-tools \
	iproute2 \
	# Network debugging and connectivity testing
	curl \
	wget \
	telnet \
	netcat-openbsd \
	dnsutils \
	# File and system inspection tools
	less \
	vim-tiny \
	htop \
	tree \
	# Archive and development tools
	unzip \
	# Advanced networking tools
	iputils-ping \
	traceroute \
	# Text processing and system analysis
	jq \
	lsof \
	strace \
	; \
	# Clean up package cache to minimize image size
	rm -rf /var/lib/apt/lists/*; \
	\
	# Verify all essential tools are available for validation and debugging
	echo "=== Verifying Debug Tools Installation for PHP 8.4 ==="; \
	command -v ps && echo "✓ ps available (required by validation script)"; \
	command -v netstat && echo "✓ netstat available (required by validation script)"; \
	command -v ss && echo "✓ ss available (required by validation script)"; \
	command -v pgrep && echo "✓ pgrep available (required by validation script)"; \
	command -v curl && echo "✓ curl available (HTTP testing)"; \
	command -v wget && echo "✓ wget available (downloads)"; \
	command -v nc && echo "✓ netcat available (network testing)"; \
	command -v ping && echo "✓ ping available (connectivity)"; \
	command -v top && echo "✓ top available (process monitoring)"; \
	command -v htop && echo "✓ htop available (interactive process viewer)"; \
	command -v jq && echo "✓ jq available (JSON processing)"; \
	command -v lsof && echo "✓ lsof available (file/socket monitoring)"; \
	command -v strace && echo "✓ strace available (system call tracing)"; \
	echo "=== Debug Tools Installation Complete for PHP 8.4 ==="

# set recommended PHP.ini settings
# see https://secure.php.net/manual/en/opcache.installation.php
RUN set -eux; \
	docker-php-ext-enable opcache; \
	{ \
		echo 'opcache.memory_consumption=128'; \
		echo 'opcache.interned_strings_buffer=8'; \
		echo 'opcache.max_accelerated_files=4000'; \
		echo 'opcache.revalidate_freq=2'; \
	} > /usr/local/etc/php/conf.d/opcache-recommended.ini
# https://wordpress.org/support/article/editing-wp-config-php/#configure-error-logging
RUN { \
# https://www.php.net/manual/en/errorfunc.constants.php
# https://github.com/docker-library/wordpress/issues/420#issuecomment-517839670
		echo 'error_reporting = E_ERROR | E_WARNING | E_PARSE | E_CORE_ERROR | E_CORE_WARNING | E_COMPILE_ERROR | E_COMPILE_WARNING | E_RECOVERABLE_ERROR'; \
		echo 'display_errors = Off'; \
		echo 'display_startup_errors = Off'; \
		echo 'log_errors = On'; \
		echo 'error_log = /dev/stderr'; \
		echo 'log_errors_max_len = 1024'; \
		echo 'ignore_repeated_errors = On'; \
		echo 'ignore_repeated_source = Off'; \
		echo 'html_errors = Off'; \
	} > /usr/local/etc/php/conf.d/error-logging.ini

# FIX: Prevent PHP-FPM pool configuration conflicts in Docker environments
# This resolves issues where www.conf and zz-docker.conf both define [www] pool
# with different listen directives, causing intermittent connection failures
RUN set -eux; \
	echo "=== Configuring PHP 8.4 FPM for Docker Environment ==="; \
	\
	# Display current PHP-FPM configuration state for debugging
	echo "Current PHP-FPM configuration files:"; \
	ls -la /usr/local/etc/php-fpm.d/ 2>/dev/null || echo "No php-fpm.d directory found yet"; \
	\
	# Check if www.conf exists and has conflicting listen directive
	if [ -f "/usr/local/etc/php-fpm.d/www.conf" ]; then \
		echo "Found www.conf, analyzing PHP 8.4 configuration..."; \
		\
		# Show current listen directives for transparency
		echo "Current listen directives in www.conf:"; \
		grep -n "listen" /usr/local/etc/php-fpm.d/www.conf || echo "  No listen directives found"; \
		\
		# Comment out any listen directive that conflicts with Docker's zz-docker.conf
		if grep -q '^listen = 127\.0\.0\.1:9000' /usr/local/etc/php-fpm.d/www.conf; then \
			echo "FIXING: Commenting out conflicting listen directive in www.conf for PHP 8.4"; \
			sed -i 's/^listen = 127\.0\.0\.1:9000/;listen = 127.0.0.1:9000 # Commented to prevent conflict with zz-docker.conf (PHP 8.4)/' /usr/local/etc/php-fpm.d/www.conf; \
		elif grep -q '^;listen = 127\.0\.0\.1:9000' /usr/local/etc/php-fpm.d/www.conf; then \
			echo "ALREADY FIXED: Conflicting listen directive already commented in www.conf"; \
		else \
			echo "INFO: No conflicting 127.0.0.1:9000 listen directive found in www.conf"; \
		fi; \
		\
		# Verify the fix was applied correctly
		if grep -q '^listen = 127\.0\.0\.1:9000' /usr/local/etc/php-fpm.d/www.conf; then \
			echo "ERROR: Failed to resolve listen directive conflict in PHP 8.4 configuration"; \
			echo "Current www.conf listen directives:"; \
			grep -n "listen" /usr/local/etc/php-fpm.d/www.conf; \
			exit 1; \
		fi; \
	else \
		echo "INFO: No www.conf found - no conflicts to resolve for PHP 8.4"; \
	fi; \
	\
	# Test PHP-FPM configuration syntax
	echo "Validating PHP 8.4 FPM configuration syntax..."; \
	php-fpm -t; \
	\
	# Display comprehensive configuration summary
	echo "=== PHP 8.4 FPM Configuration Summary ==="; \
	if [ -f "/usr/local/etc/php-fpm.d/www.conf" ]; then \
		echo "www.conf final listen configuration:"; \
		grep -n "listen.*127.0.0.1:9000" /usr/local/etc/php-fpm.d/www.conf || echo "  ✓ No active conflicting listen directive"; \
	fi; \
	if [ -f "/usr/local/etc/php-fpm.d/zz-docker.conf" ]; then \
		echo "zz-docker.conf configuration:"; \
		cat /usr/local/etc/php-fpm.d/zz-docker.conf; \
	else \
		echo "  Note: zz-docker.conf will be created by Docker at runtime"; \
	fi; \
	\
	# Final configuration test for PHP 8.4
	echo "Final PHP 8.4 FPM configuration test:"; \
	php-fpm -tt 2>&1 | head -5 || true; \
	echo "=== PHP 8.4 FPM Docker Configuration Complete ==="

RUN set -eux; \
	version='6.8.1'; \
	sha1='52d5f05c96a9155f78ed84700264307e5dea14b4'; \
	\
	curl -o wordpress.tar.gz -fL "https://wordpress.org/wordpress-$version.tar.gz"; \
	echo "$sha1 *wordpress.tar.gz" | sha1sum -c -; \
	\
# upstream tarballs include ./wordpress/ so this gives us /usr/src/wordpress
	tar -xzf wordpress.tar.gz -C /usr/src/; \
	rm wordpress.tar.gz; \
	\
# https://wordpress.org/support/article/htaccess/
	[ ! -e /usr/src/wordpress/.htaccess ]; \
	{ \
		echo '# BEGIN WordPress'; \
		echo ''; \
		echo 'RewriteEngine On'; \
		echo 'RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]'; \
		echo 'RewriteBase /'; \
		echo 'RewriteRule ^index\.php$ - [L]'; \
		echo 'RewriteCond %{REQUEST_FILENAME} !-f'; \
		echo 'RewriteCond %{REQUEST_FILENAME} !-d'; \
		echo 'RewriteRule . /index.php [L]'; \
		echo ''; \
		echo '# END WordPress'; \
	} > /usr/src/wordpress/.htaccess; \
	\
	chown -R www-data:www-data /usr/src/wordpress; \
# pre-create wp-content (and single-level children) for folks who want to bind-mount themes, etc so permissions are pre-created properly instead of root:root
# wp-content/cache: https://github.com/docker-library/wordpress/issues/534#issuecomment-705733507
	mkdir wp-content; \
	for dir in /usr/src/wordpress/wp-content/*/ cache; do \
		dir="$(basename "${dir%/}")"; \
		mkdir "wp-content/$dir"; \
	done; \
	chown -R www-data:www-data wp-content; \
	chmod -R 1777 wp-content

# Add an advanced debug script optimized for PHP 8.4 container troubleshooting
RUN set -eux; \
	{ \
	echo '#!/bin/bash'; \
	echo '# WordPress PHP 8.4 FPM Container Advanced Debug Helper'; \
	echo 'echo "=== PHP 8.4 FPM Container Debug Information ==="'; \
	echo 'echo "Container: php:8.4-fpm with WordPress 6.8.1"'; \
	echo 'echo "Debug Date: $(date)"'; \
	echo 'echo "Uptime: $(uptime -p 2>/dev/null || uptime)"'; \
	echo 'echo ""'; \
	echo 'echo "PHP 8.4 Details:"'; \
	echo 'php --version | head -1'; \
	echo 'echo "OPcache: $(php -m | grep -i opcache || echo "Not loaded")"'; \
	echo 'echo "Key Extensions: $(php -m | grep -E '\''(mysqli|gd|imagick|zip|bcmath|intl)'\'' | tr '\''\n'\'' '\'' '\'' | sed '\''s/ $//'\'')'; \
	echo 'echo "Memory Limit: $(php -r '\''echo ini_get("memory_limit");'\'')"'; \
	echo 'echo "Max Execution Time: $(php -r '\''echo ini_get("max_execution_time");'\'')"'; \
	echo 'echo ""'; \
	echo 'echo "PHP-FPM Configuration Status:"'; \
	echo 'php-fpm -t 2>/dev/null && echo "✓ Configuration Valid" || echo "✗ Configuration Error"'; \
	echo 'echo ""'; \
	echo 'echo "PHP-FPM Pool Configuration:"'; \
	echo 'echo "Configuration files: $(ls -1 /usr/local/etc/php-fpm.d/*.conf 2>/dev/null | wc -l)"'; \
	echo 'echo "Listen directives:"'; \
	echo 'grep -Hn '\''listen'\'' /usr/local/etc/php-fpm.d/*.conf 2>/dev/null | head -10 || echo "  No listen directives found"'; \
	echo 'echo ""'; \
	echo 'echo "Process Information:"'; \
	echo 'echo "Main process: $(cat /proc/1/comm 2>/dev/null || echo "Unknown")"'; \
	echo 'echo "PHP-FPM processes:"'; \
	echo 'ps aux | grep -E '\''[p]hp-fpm'\'' | wc -l | xargs -I {} echo "  {} processes running"'; \
	echo 'ps aux | grep -E '\''[p]hp-fpm'\'' | head -3'; \
	echo 'echo ""'; \
	echo 'echo "Network Configuration:"'; \
	echo 'echo "Listening services:"'; \
	echo 'if command -v netstat >/dev/null 2>&1; then'; \
	echo '  netstat -tuln | grep LISTEN'; \
	echo 'elif command -v ss >/dev/null 2>&1; then'; \
	echo '  ss -tuln | grep LISTEN'; \
	echo 'else'; \
	echo '  echo "  No network tools available"'; \
	echo 'fi'; \
	echo 'echo "Port 9000 Status:"'; \
	echo 'if netstat -tuln 2>/dev/null | grep -q :9000; then'; \
	echo '  echo "✓ Port 9000 is listening"'; \
	echo 'else'; \
	echo '  echo "⚠ Port 9000 not found in netstat"'; \
	echo 'fi'; \
	echo 'timeout 3 bash -c '\''</dev/tcp/127.0.0.1/9000'\'' 2>/dev/null && echo "✓ Port 9000 accepts connections" || echo "⚠ Port 9000 connection failed"'; \
	echo 'echo ""'; \
	echo 'echo "System Resources:"'; \
	echo 'echo "Memory Usage:"'; \
	echo 'free -h | grep -E '\''(Mem|Swap)'\'''; \
	echo 'echo "Load Average: $(uptime | sed '\''s/.*load average: //'\'')"'; \
	echo 'echo "Disk Usage:"'; \
	echo 'df -h / | tail -1 | awk '\''{printf "Root: %s/%s (%s used)\n", $3, $2, $5}'\'''; \
	echo 'echo ""'; \
	echo 'echo "WordPress Environment:"'; \
	echo 'if [ -f /var/www/html/wp-config.php ]; then'; \
	echo '  echo "✓ WordPress configuration found"'; \
	echo '  echo "PHP files: $(find /var/www/html -name '\''*.php'\'' | wc -l)"'; \
	echo '  echo "WordPress version: $(grep wp_version /var/www/html/wp-includes/version.php 2>/dev/null | head -1 || echo "Unknown")"'; \
	echo 'else'; \
	echo '  echo "⚠ WordPress not configured in /var/www/html"'; \
	echo '  echo "Available files: $(ls -la /var/www/html/ 2>/dev/null | wc -l) items"'; \
	echo 'fi'; \
	echo 'echo ""'; \
	echo 'echo "Debug Tools Availability:"'; \
	echo 'echo "System: $(command -v ps >/dev/null && echo "✓ ps" || echo "✗ ps") $(command -v top >/dev/null && echo "✓ top" || echo "✗ top") $(command -v htop >/dev/null && echo "✓ htop" || echo "✗ htop")"'; \
	echo 'echo "Network: $(command -v netstat >/dev/null && echo "✓ netstat" || echo "✗ netstat") $(command -v ss >/dev/null && echo "✓ ss" || echo "✗ ss") $(command -v curl >/dev/null && echo "✓ curl" || echo "✗ curl")"'; \
	echo 'echo "Development: $(command -v git >/dev/null && echo "✓ git" || echo "✗ git") $(command -v jq >/dev/null && echo "✓ jq" || echo "✗ jq") $(command -v strace >/dev/null && echo "✓ strace" || echo "✗ strace")"'; \
	echo 'echo ""'; \
	echo 'echo "Quick Health Checks:"'; \
	echo 'echo "PHP syntax: $(echo '\''<?php echo "OK"; ?>'\'' | php 2>/dev/null || echo "Error")"'; \
	echo 'echo "PHP-FPM syntax: $(php-fpm -t 2>/dev/null && echo "Valid" || echo "Invalid")"'; \
	echo 'echo "File permissions: $(ls -ld /var/www/html 2>/dev/null | awk '\''{print $1, $3, $4}'\'' || echo "Cannot check")"'; \
	echo 'echo "=== PHP 8.4 Debug Complete ==="'; \
	} > /usr/local/bin/debug-container; \
	chmod +x /usr/local/bin/debug-container; \
	echo "✓ Advanced PHP 8.4 debug script created at /usr/local/bin/debug-container"

VOLUME /var/www/html

# Add comprehensive labels for PHP 8.4 identification and debugging
LABEL maintainer="WordPress PHP 8.4 FPM with Advanced Debug Tools" \
      php.version="8.4" \
      wordpress.version="6.8.1" \
      imagick.version="3.8.0" \
      version="1.0" \
      description="WordPress PHP 8.4 FPM container with PHP-FPM configuration fix and comprehensive debug tools" \
      debug.tools="ps,netstat,ss,curl,wget,nc,ping,htop,git,jq,lsof,strace" \
      config.fix="php-fpm-listen-conflict-resolved" \
      validation.script="compatible" \
      build.optimized="production-ready"